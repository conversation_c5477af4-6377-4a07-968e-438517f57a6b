#!/usr/bin/env node

/**
 * User Creation Script for BITS DataScience Platform
 *
 * This script creates users with different roles (admin, instructor, student)
 * Usage: node scripts/create-users.js [options]
 *
 * Options:
 *   --role <role>     Create user with specific role (admin, instructor, student)
 *   --count <number>  Number of users to create (default: 1)
 *   --batch           Create a batch of users with all roles
 *   --interactive     Interactive mode to input user details
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from 'dotenv';
import bcrypt from 'bcryptjs';
import readline from 'readline';

const predefinedUsers = {
  admin: [
    {
      name: 'admin',
      password: 'admin@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        department: 'Computer Science',
        employee_id: 'ADM001',
        access_level: 'full',
        created_by: 'script',
        source: 'predefined'
      }
    }
  ],
  instructor: [
    {
      name: 'instructor1',
      password: 'instructor1@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        department: 'Computer Science',
        employee_id: 'INS001',
        office_hours: 'Mon-Wed 2-4 PM',
        research_interests: ['Machine Learning', 'Data Mining'],
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'instructor2',
      password: 'instructor2@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        department: 'Information Technology',
        employee_id: 'INS002',
        office_hours: 'Tue-Thu 3-5 PM',
        research_interests: ['Artificial Intelligence', 'Database Systems'],
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'instructor3',
      password: 'instructor3@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        department: 'Data Science',
        employee_id: 'INS003',
        office_hours: 'Wed-Fri 1-3 PM',
        research_interests: ['Software Engineering', 'Computer Networks'],
        created_by: 'script',
        source: 'predefined'
      }
    }
  ],
  student: [
    {
      name: 'student1',
      password: 'student1@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        student_id: '2021001001',
        year: '4th',
        branch: 'CSE',
        cgpa: 8.5,
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'student2',
      password: 'student2@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        student_id: '2021001002',
        year: '3rd',
        branch: 'IT',
        cgpa: 7.8,
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'student3',
      password: 'student3@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        student_id: '2021001003',
        year: '2nd',
        branch: 'ECE',
        cgpa: 9.1,
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'student4',
      password: 'student4@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        student_id: '2021001004',
        year: '1st',
        branch: 'EEE',
        cgpa: 8.2,
        created_by: 'script',
        source: 'predefined'
      }
    },
    {
      name: 'student5',
      password: 'student5@123',
      email: '<EMAIL>',
      status: 'active',
      metadata: {
        student_id: '2021001005',
        year: '4th',
        branch: 'CSE',
        cgpa: 9.3,
        created_by: 'script',
        source: 'predefined'
      }
    }
  ]
};

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
config({ path: join(__dirname, '..', '.env') });

// Import models
import { sequelize } from '../src/config/database.js';
import User from '../src/models/User.js';
import Role from '../src/models/Role.js';
import UserRole from '../src/models/UserRole.js';

// Import associations
import '../src/models/associations.js';

class UserCreator {
  constructor() {
    this.roles = {};
  }

  async initialize() {
    try {
      await sequelize.authenticate();
      console.log('✅ Database connection established');

      // Load existing roles
      const roles = await Role.findAll();
      roles.forEach(role => {
        this.roles[role.name] = role;
      });

      console.log('📋 Available roles:', Object.keys(this.roles).join(', '));
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      process.exit(1);
    }
  }

  generateUserData(role = 'student', index = 0) {
    const users = predefinedUsers[role];
    if (!users || !users[index]) {
      throw new Error(
        `No predefined user data found for role: ${role}, index: ${index}`
      );
    }

    const userData = users[index];

    return {
      name: userData.name,
      email: userData.email,
      password_hash: userData.password, // Will be hashed by model hook
      status: userData.status || 'active',
      profile_picture: null, // Remove faker-generated avatar
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: {
          email: true,
          push: false
        }
      },
      metadata: userData.metadata
    };
  }

  getRoleSpecificData(role) {
    switch (role) {
      case 'admin':
      case 'super_admin':
        return {
          department: faker.helpers.arrayElement([
            'Computer Science',
            'Information Technology',
            'Data Science'
          ]),
          employee_id: faker.string.alphanumeric(8).toUpperCase(),
          access_level: 'full'
        };

      case 'instructor':
        return {
          department: faker.helpers.arrayElement([
            'Computer Science',
            'Information Technology',
            'Data Science',
            'Mathematics'
          ]),
          employee_id: faker.string.alphanumeric(8).toUpperCase(),
          office_hours: 'Mon-Wed 2-4 PM',
          research_interests: faker.helpers.arrayElements(
            [
              'Machine Learning',
              'Data Mining',
              'Artificial Intelligence',
              'Database Systems',
              'Software Engineering',
              'Computer Networks'
            ],
            { min: 1, max: 3 }
          )
        };

      case 'student':
        return {
          student_id: faker.string.numeric(10),
          year: faker.helpers.arrayElement(['1st', '2nd', '3rd', '4th']),
          branch: faker.helpers.arrayElement(['CSE', 'IT', 'ECE', 'EEE']),
          cgpa: parseFloat(
            faker.number.float({ min: 6.0, max: 10.0, fractionDigits: 2 })
          )
        };

      case 'ta':
        return {
          student_id: faker.string.numeric(10),
          year: faker.helpers.arrayElement(['3rd', '4th', 'Masters', 'PhD']),
          branch: faker.helpers.arrayElement(['CSE', 'IT', 'ECE', 'EEE']),
          ta_subjects: faker.helpers.arrayElements(
            [
              'Data Structures',
              'Algorithms',
              'Database Systems',
              'Web Development'
            ],
            { min: 1, max: 2 }
          )
        };

      default:
        return {};
    }
  }

  async createUser(userData, roleName) {
    try {
      // Create user
      const user = await User.create(userData);
      console.log(`✅ Created user: ${user.name} (${user.email})`);

      // Assign role
      if (this.roles[roleName]) {
        await UserRole.create({
          user_id: user.id,
          role_id: this.roles[roleName].id,
          is_primary: true,
          assigned_at: new Date()
        });
        console.log(`🎭 Assigned role: ${roleName}`);
      } else {
        console.warn(`⚠️  Role '${roleName}' not found`);
      }

      return user;
    } catch (error) {
      console.error(`❌ Failed to create user:`, error.message);
      throw error;
    }
  }

  async createBatchUsers() {
    console.log('🚀 Creating batch of users with different roles...\n');

    const usersToCreate = [
      { role: 'super_admin', count: 1 },
      { role: 'admin', count: 2 },
      { role: 'instructor', count: 5 },
      { role: 'ta', count: 3 },
      { role: 'student', count: 20 }
    ];

    const createdUsers = [];

    for (const { role, count } of usersToCreate) {
      console.log(`Creating ${count} ${role}(s)...`);

      for (let i = 0; i < count; i++) {
        const userData = this.generateUserData(role);
        const user = await this.createUser(userData, role);
        createdUsers.push({ user, role });
      }
      console.log('');
    }

    return createdUsers;
  }

  async createSpecificUsers(role, count = 1) {
    console.log(`🚀 Creating ${count} user(s) with role: ${role}\n`);

    const createdUsers = [];

    for (let i = 0; i < count; i++) {
      const userData = this.generateUserData(role);
      const user = await this.createUser(userData, role);
      createdUsers.push({ user, role });
    }

    return createdUsers;
  }

  async createInteractiveUser() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const question = prompt =>
      new Promise(resolve => rl.question(prompt, resolve));

    try {
      console.log('🎯 Interactive User Creation\n');

      const name = await question('Enter user name: ');
      const email = await question('Enter email: ');
      const role = await question(
        `Enter role (${Object.keys(this.roles).join(', ')}): `
      );

      if (!this.roles[role]) {
        console.error(`❌ Invalid role: ${role}`);
        rl.close();
        return null;
      }

      const userData = {
        name: name.trim(),
        email: email.trim().toLowerCase(),
        password_hash: 'password123',
        status: 'active',
        preferences: { theme: 'light', language: 'en' },
        metadata: { created_by: 'interactive-script', source: 'manual' }
      };

      rl.close();

      const user = await this.createUser(userData, role);
      return [{ user, role }];
    } catch (error) {
      rl.close();
      throw error;
    }
  }

  async displaySummary(users) {
    console.log('\n📊 Summary of Created Users:');
    console.log('='.repeat(50));

    const roleCount = {};
    users.forEach(({ role }) => {
      roleCount[role] = (roleCount[role] || 0) + 1;
    });

    Object.entries(roleCount).forEach(([role, count]) => {
      console.log(`${role.padEnd(15)}: ${count} user(s)`);
    });

    console.log('='.repeat(50));
    console.log(`Total users created: ${users.length}`);

    // Display sample login credentials
    console.log('\n🔑 Sample Login Credentials:');
    console.log('Password for all users: password123');
    console.log('\nSample users:');

    const sampleUsers = users.slice(0, 5);
    sampleUsers.forEach(({ user, role }) => {
      console.log(`${role.padEnd(12)}: ${user.email}`);
    });
  }

  async cleanup() {
    await sequelize.close();
    console.log('\n✅ Database connection closed');
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const userCreator = new UserCreator();

  try {
    await userCreator.initialize();

    let createdUsers = [];

    if (args.includes('--batch')) {
      createdUsers = await userCreator.createBatchUsers();
    } else if (args.includes('--interactive')) {
      createdUsers = await userCreator.createInteractiveUser();
      if (!createdUsers) {
        console.log('❌ User creation cancelled');
        process.exit(1);
      }
    } else if (args.includes('--role')) {
      const roleIndex = args.indexOf('--role');
      const role = args[roleIndex + 1];
      const countIndex = args.indexOf('--count');
      const count = countIndex !== -1 ? parseInt(args[countIndex + 1]) || 1 : 1;

      if (!role) {
        console.error('❌ Please specify a role with --role <role>');
        process.exit(1);
      }

      createdUsers = await userCreator.createSpecificUsers(role, count);
    } else {
      // Default: create one student
      createdUsers = await userCreator.createSpecificUsers('student', 1);
    }

    await userCreator.displaySummary(createdUsers);
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    await userCreator.cleanup();
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default UserCreator;
