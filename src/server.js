import SequelizeStoreInit from 'connect-session-sequelize';
import cors from 'cors';
import express from 'express';
import session from 'express-session';
import helmet from 'helmet';
import morgan from 'morgan';
import { sequelize } from './config/database.js';
import logger from './config/logger.js';
import setupSwagger from './config/swagger.js';
import { verifyToken } from './middlewares/auth.js';
import { errorHandler, NotFoundError } from './middlewares/errorHandler.js';
import passport from 'passport';
import './config/passport.js';
// Import routes
import authRoutes from './routes/auth.js';
import courseRoutes from './routes/courses.js';
import gradeRoutes from './routes/grades.js';
import lmsRoutes from './routes/lms.js';
import ltiRoutes from './routes/lti.js';
import { getJWKS } from './controllers/ltiController.js';
import projectRoutes from './routes/projects.js';
import roleRoutes from './routes/roles.js';
import s3Routes from './routes/s3.js';
import sandboxRoutes from './routes/sandbox.js';
import submissionRoutes from './routes/submissions.js';
import userRoutes from './routes/users.js';
import workspaceRoutes from './routes/workspace.js';

// Import associations
import './models/associations.js';

const app = express();
app.use(passport.initialize());
const PORT = process.env.PORT || 5000;

// --------------------
// Process-level crash logging for debugging
// --------------------
process.on('uncaughtException', err => {
  console.error(
    {
      name: err.name,
      message: err.message,
      stack: err.stack
    },
    'Uncaught Exception — shutting down'
  );
  process.exit(1);
});

process.on('unhandledRejection', reason => {
  console.error(
    {
      reason:
        reason instanceof Error
          ? { name: reason.name, message: reason.message, stack: reason.stack }
          : reason
    },
    'Unhandled Promise Rejection — shutting down'
  );
  process.exit(1);
});

process.on('warning', warning => {
  console.warn(
    {
      name: warning.name,
      message: warning.message,
      stack: warning.stack
    },
    'Process warning'
  );
});
// --------------------

// Session store setup
const SequelizeStore = SequelizeStoreInit(session.Store);
const sessionStore = new SequelizeStore({
  db: sequelize,
  tableName: 'sessions',
  checkExpirationInterval: 15 * 60 * 1000,
  expiration: 24 * 60 * 60 * 1000
});

// Middleware
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        frameSrc: ["'self'", 'https:'],
        frameAncestors: ["'self'", 'https:']
      }
    }
  })
);

app.use(
  cors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })
);

// HTTP logging
morgan.token('id', req => req.id || '-');
app.use(
  morgan(':id :method :url :status :response-time ms', {
    stream: { write: message => console.log(message.trim()) }
  })
);

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Session
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000
    },
    name: 'bits.session.id'
  })
);

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', verifyToken, userRoutes);
app.use('/api/courses', verifyToken, courseRoutes);
app.use('/api/projects', verifyToken, projectRoutes);
app.use('/api/submissions', verifyToken, submissionRoutes);
app.use('/api/grades', verifyToken, gradeRoutes);
app.use('/api/roles', verifyToken, roleRoutes);
app.use('/api/s3', verifyToken, s3Routes);
app.use('/api/lms', verifyToken, lmsRoutes);
app.use('/api/sandbox', sandboxRoutes);
app.use('/api/workspace', workspaceRoutes);

// adjust path if needed
// app.use('/api/sandbox', sandboxRoutes);

// LTI routes (no authentication required for initial endpoints)

// Expose JWKS endpoint at root
app.get('/.well-known/jwks.json', getJWKS);

app.use('/api/lti', ltiRoutes);

// Swagger
setupSwagger(app);

// Error handling
app.use((req, res, next) => {
  next(new NotFoundError());
});
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    // Only connect to database if not in test mode
    if (process.env.NODE_ENV !== 'test') {
      await sequelize.authenticate();
      logger.info('Database connection established successfully');

      await sessionStore.sync();
      logger.info('Session store synchronized');

      // Sync database models - temporarily disabled due to sync issues

      if (process.env.NODE_ENV === 'development') {
        try {
          await sequelize.sync({});
          logger.info('Database models synchronized');
        } catch (syncError) {
          logger.error('Database sync error:', {
            message: syncError.message,
            stack: syncError.stack,
            name: syncError.name,
            code: syncError.code,
            fields: syncError.fields,
            parent: syncError.parent,
            original: syncError.original
          });
          if (syncError?.parent) {
            logger.error('Sequelize parent error:', {
              message: syncError.parent.message,
              detail: syncError.parent.detail,
              hint: syncError.parent.hint,
              code: syncError.parent.code,
              table: syncError.parent.table,
              schema: syncError.parent.schema,
              stack: syncError.parent.stack
            });
          }
        }
      }
    }

    // Start server
    const server = app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`API Documentation: http://localhost:${PORT}/api-docs`);
      logger.info(`LTI Configuration: http://localhost:${PORT}/api/lti/config`);
    });

    // Graceful shutdown
    const gracefulShutdown = signal => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      server.close(() => {
        logger.info('HTTP server closed');

        if (process.env.NODE_ENV !== 'test') {
          sequelize
            .close()
            .then(() => {
              logger.info('Database connection closed');
              process.exit(0);
            })
            .catch(err => {
              logger.error('Error closing database connection:', err);
              process.exit(1);
            });
        } else {
          process.exit(0);
        }
      });
    };

    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Only start server if not in test mode
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

export default app;
