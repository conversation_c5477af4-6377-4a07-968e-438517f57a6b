import { User, Role, Permission, CourseEnrollment, Course } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import logger from '../config/logger.js';

/**
 * @desc    Get all users with pagination and filtering
 * @route   GET /api/users
 * @access  Private (Admin, Instructor)
 */
export const getUsers = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    role,
    status,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};
  const includeClause = [{
    model: Role,
    as: 'roles',
    include: [{
      model: Permission,
      as: 'permissions'
    }]
  }];

  // Add search functionality
  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  // Filter by role
  if (role) {
    includeClause[0].where = { name: role };
    includeClause[0].required = true;
  }

  const { count, rows: users } = await User.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]],
    distinct: true
  });

  // Transform user data for response
  const transformedUsers = users.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    status: user.status,
    lmsUserId: user.lms_user_id,
    createdAt: user.created_at,
    roles: user.roles?.map(role => ({
      id: role.id,
      name: role.name
    })) || []
  }));

  res.json({
    success: true,
    data: {
      users: transformedUsers,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get user by ID
 * @route   GET /api/users/:id
 * @access  Private
 */
export const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findByPk(id, {
    include: [{
      model: Role,
      as: 'roles',
      include: [{
        model: Permission,
        as: 'permissions'
      }]
    }, {
      model: CourseEnrollment,
      as: 'enrollments',
      include: [{
        model: Course,
        as: 'course'
      }]
    }]
  });

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  // Check if current user can view this profile
  const canViewProfile = req.user.id === user.id || 
                        req.userRoles.includes('admin') || 
                        req.userRoles.includes('instructor');

  if (!canViewProfile) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view this profile'
    });
  }

  const userResponse = {
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    status: user.status,
    lmsUserId: user.lms_user_id,
    preferences: user.preferences,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
    roles: user.roles?.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions?.map(permission => permission.key) || []
    })) || [],
    enrollments: user.enrollments?.map(enrollment => ({
      id: enrollment.id,
      roleInCourse: enrollment.role_in_course,
      enrollmentStatus: enrollment.enrollment_status,
      enrolledAt: enrollment.enrolled_at,
      course: {
        id: enrollment.course.id,
        name: enrollment.course.name,
        code: enrollment.course.code,
        term: enrollment.course.term
      }
    })) || []
  };

  res.json({
    success: true,
    user: userResponse
  });
});

/**
 * @desc    Update user profile
 * @route   PUT /api/users/:id
 * @access  Private
 */
export const updateUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, profilePicture, preferences } = req.body;

  const user = await User.findByPk(id);

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  // Check if current user can update this profile
  const canUpdateProfile = req.user.id === user.id || req.userRoles.includes('admin');

  if (!canUpdateProfile) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to update this profile'
    });
  }

  // Update allowed fields
  const updateData = {};
  if (name) updateData.name = name;
  if (profilePicture) updateData.profile_picture = profilePicture;
  if (preferences) updateData.preferences = { ...user.preferences, ...preferences };

  await user.update(updateData);

  logger.info(`User profile updated: ${user.email} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      profilePicture: user.profile_picture,
      preferences: user.preferences
    }
  });
});

/**
 * @desc    Update user status (Admin only)
 * @route   PATCH /api/users/:id/status
 * @access  Private (Admin only)
 */
export const updateUserStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!['active', 'inactive', 'suspended'].includes(status)) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid status. Must be: active, inactive, or suspended'
    });
  }

  const user = await User.findByPk(id);

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  await user.update({ status });

  logger.info(`User status updated: ${user.email} to ${status} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'User status updated successfully',
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      status: user.status
    }
  });
});

/**
 * @desc    Assign role to user (Admin only)
 * @route   POST /api/users/:id/roles
 * @access  Private (Admin only)
 */
export const assignUserRole = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { roleId, isPrimary = false } = req.body;

  if (!roleId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Role ID is required'
    });
  }

  const user = await User.findByPk(id);
  const role = await Role.findByPk(roleId);

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  if (!role) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Role not found'
    });
  }

  // Check if user already has this role
  const existingRole = await user.hasRole(role);
  if (existingRole) {
    return res.status(409).json({
      error: 'Conflict',
      message: 'User already has this role'
    });
  }

  // Add role to user
  await user.addRole(role, {
    through: {
      assigned_by: req.user.id,
      is_primary: isPrimary
    }
  });

  logger.info(`Role assigned: ${role.name} to ${user.email} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Role assigned successfully'
  });
});

/**
 * @desc    Remove role from user (Admin only)
 * @route   DELETE /api/users/:id/roles/:roleId
 * @access  Private (Admin only)
 */
export const removeUserRole = asyncHandler(async (req, res) => {
  const { id, roleId } = req.params;

  const user = await User.findByPk(id);
  const role = await Role.findByPk(roleId);

  if (!user) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'User not found'
    });
  }

  if (!role) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Role not found'
    });
  }

  // Remove role from user
  await user.removeRole(role);

  logger.info(`Role removed: ${role.name} from ${user.email} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Role removed successfully'
  });
});

/**
 * @desc    Get user statistics (Admin only)
 * @route   GET /api/users/statistics
 * @access  Private (Admin only)
 */
export const getUserStatistics = asyncHandler(async (req, res) => {
  const totalUsers = await User.count();
  const activeUsers = await User.count({ where: { status: 'active' } });
  const inactiveUsers = await User.count({ where: { status: 'inactive' } });
  const suspendedUsers = await User.count({ where: { status: 'suspended' } });

  // Get user counts by role
  const roleStats = await Role.findAll({
    include: [{
      model: User,
      as: 'users',
      attributes: []
    }],
    attributes: [
      'name',
      [sequelize.fn('COUNT', sequelize.col('users.id')), 'userCount']
    ],
    group: ['Role.id', 'Role.name']
  });

  // Recent registrations (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentRegistrations = await User.count({
    where: {
      created_at: {
        [Op.gte]: thirtyDaysAgo
      }
    }
  });

  res.json({
    success: true,
    statistics: {
      total: totalUsers,
      active: activeUsers,
      inactive: inactiveUsers,
      suspended: suspendedUsers,
      recentRegistrations,
      roleDistribution: roleStats.map(role => ({
        role: role.name,
        count: parseInt(role.getDataValue('userCount'))
      }))
    }
  });
});